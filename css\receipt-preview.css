/**
 * Receipt Preview Styles
 * Extracted from inline CSS in main.js
 */

/* Main Receipt Container with Elegant Certificate Border */
.receipt-container {
    position: relative;
    max-width: 8.5in;
    margin: 0 auto;
    padding: 2rem 2.5rem;
    font-family: 'Courier New', monospace;
    font-size: 12pt;
    line-height: 1.4;
    background: white;
    border-radius: 60px; /* extra round corners */
    box-shadow:
        0 0 0 3px #D4AF37,
        0 0 0 6px white,
        0 0 0 9px #B8860B,
        0 0 0 12px white,
        0 0 0 15px #DAA520,
        0 8px 30px rgba(0, 0, 0, 0.2),
        inset 0 0 0 2px #F5DEB3;
    /* Elegant decorative pattern */
    background-image:
        radial-gradient(circle at 20px 20px, #F5DEB3 2px, transparent 2px),
        radial-gradient(circle at 60px 60px, #F5DEB3 1px, transparent 1px);
    background-size: 80px 80px, 40px 40px;
    background-position: 0 0, 20px 20px;
}

.receipt-container::before,
.receipt-container::after {
    content: '';
    position: absolute;
    z-index: -1;
}

.receipt-container::before {
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 1px solid #D4AF37;
    border-radius: 48px; /* match outer rounding feel */
    background: linear-gradient(45deg,
        transparent 0%,
        rgba(212, 175, 55, 0.1) 25%,
        transparent 50%,
        rgba(212, 175, 55, 0.1) 75%,
        transparent 100%);
}

.receipt-container::after {
    top: 30px;
    left: 30px;
    right: 30px;
    bottom: 30px;
    display: none !important;
}

.receipt-inner {
    position: relative;
    z-index: 1;
}

/* Logo Section */
.receipt-logo {
    text-align: center;
    margin-bottom: 1rem;
}

.receipt-logo img {
    max-height: 100px;
    max-width: 200px;
    object-fit: contain;
}

/* Receipt Header */
.preview-receipt-header {
    text-align: center;
    border-bottom: 2px solid #333;
    padding-bottom: 0.5rem;
    margin-bottom: 0.75rem;
}

.receipt-title {
    font-size: 1.8rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.receipt-company-info {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.3;
}

/* Receipt Info Section */
.preview-receipt-info {
    display: flex;
    justify-content: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ddd;
}

.receipt-number {
    font-size: 1.1rem;
    font-weight: bold;
    color: #0d6efd;
}

/* Customer Information */
.preview-customer-info {
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background-color: #8efdeb;
    border-radius: 12px;
}

.preview-customer-info h6 {
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    color: #333;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
}

.customer-field {
    display: flex;
    align-items: center;
    margin-bottom: 0.35rem;
}

.customer-field-label {
    width: 90px;
    font-weight: bold;
    color: #555;
}

.customer-field-value {
    display: inline-block;
    min-width: 320px;
    height: 20px;
    line-height: 20px;
    color: #333;
    border-bottom: 1px solid #333;
}

.customer-field-value.handwrite-space {
    min-height: 25px;
    border-bottom: 2px solid #333;
    background: linear-gradient(to right, transparent 0%, transparent 98%, #ddd 98%, #ddd 100%);
}

/* Items Table */
.receipt-items {
    margin-bottom: 0.75rem;
}

.items-header {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid #ddd;
}

.items-header h6 {
    margin: 0;
    color: #333;
    font-weight: bold;
}

.item-number {
    font-weight: bold;
    color: #0d6efd;
    background-color: #f8f9fa;
}

.receipt-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.receipt-table thead tr {
    background-color: #81c0ff;
}

.receipt-table th {
    padding: 4px 2px;
    color: #ffffff;
}

.receipt-table tbody tr {
    border-bottom: 1px solid #ddd;
}

.receipt-table td {
    padding: 0.75rem 0.5rem;
}

.receipt-table .text-left {
    text-align: left;
}

.receipt-table .text-center {
    text-align: center;
}

.receipt-table .text-right {
    text-align: right;
}

.item-name {
    font-weight: bold;
}

.item-description {
    color: #666;
    font-size: 0.9em;
}

.item-category {
    color: #0dcaf0;
    font-size: 0.9em;
}

/* Totals Section */
.receipt-totals {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 2px solid #333;
}

.totals-table {
    width: 100%;
    max-width: 300px;
    margin-left: auto;
}

.totals-table td {
    padding: 0.5rem 1rem;
    border: none;
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.totals-table td.label {
    text-align: right;
    color: #555;
}

.totals-table td.amount {
    text-align: right;
}

.totals-table tr.total-row {
    font-size: 1.1rem;
    border-top: 1px solid #333;
}

.totals-table tr.total-row td {
    color: #333;
}

/* Payment Method Section */
.payment-method {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.payment-method-title {
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    font-weight: bold;
    color: #333;
}

.payment-options {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 15px;
}

.payment-option-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 80px;
    padding: 12px 8px;
    background: white;
    border: 2px solid #D4AF37;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.payment-option-button:hover {
    border-color: #B8860B;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.payment-checkbox {
    position: relative;
    width: 20px;
    height: 20px;
    background: white;
    border: 2px solid #333;
    border-radius: 4px;
}

.payment-checkbox::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 6px;
    width: 6px;
    height: 10px;
    border: solid transparent;
    border-width: 0 2px 2px 0;
    opacity: 0;
    transform: rotate(45deg);
}

.payment-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
    text-align: center;
}

/* Notes Section */
.receipt-notes {
    margin-top: 0.75rem;
    padding: 0.5rem;
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    border-radius: 4px;
}

.receipt-notes h6 {
    margin-bottom: 0.5rem;
    color: #856404;
}

.receipt-notes p {
    margin: 0;
    color: #856404;
}

/* Signature Section */
.signature-section {
    margin-top: 1.5rem;
    padding-top: 1rem;
    font-size: 1rem;
    text-align: left;
    color: #333;
    border-top: 2px solid #333;
}

.signature-labels-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0 1rem;
}

.signature-label-item {
    flex: 1;
    text-align: center;
}

.signature-label {
    font-size: 0.95rem;
    font-weight: bold;
    color: #333;
}

.signature-lines-area {
    margin-top: 0.5rem;
    padding: 0 1rem;
}

.signature-line-space {
    position: relative;
    width: 100%;
    height: 60px;
    border-bottom: 2px solid #333;
    background: linear-gradient(
        to right,
        transparent 0%,
        transparent 32%,
        #ddd 32%,
        #ddd 33%,
        transparent 33%,
        transparent 65%,
        #ddd 65%,
        #ddd 66%,
        transparent 66%,
        transparent 100%
    );
}

.signature-line-space::before,
.signature-line-space::after {
    content: '';
    position: absolute;
    top: 0;
    width: 1px;
    height: 100%;
    background: #ddd;
}

.signature-line-space::before {
    left: 33%;
}

.signature-line-space::after {
    right: 33%;
}

/* Print Styles */
@media print {
    .receipt-container {
        /* Full-bleed print: fill entire Letter page */
        width: 8.5in !important;
        height: 11in !important;
        max-width: 8.5in !important;
        margin: 0 !important;
        padding: 0.25in !important;
        border: 3px solid #D4AF37 !important;
        border-radius: 60px !important;
        box-shadow: none !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
        page-break-inside: avoid;
        background-image: none !important;
    }

    .receipt-container::before {
        /* Stretch decorative borders to the page edges for full-bleed */
        top: 0.1in !important;
        left: 0.1in !important;
        right: 0.1in !important;
        bottom: 0.1in !important;
        border: 3px solid #D4AF37 !important;
        border-radius: 52px !important; /* inner ring rounding adjusted to match 60px outer */
        opacity: 0.9 !important;
        box-shadow:
            0 0 0 6px white,
            0 0 0 9px #B8860B,
            0 0 0 12px white,
            0 0 0 15px #DAA520,
            inset 0 0 0 2px #F5DEB3 !important;
    }

    /* subtle inner ring, not dashed */
    .receipt-container::after {
        display: block !important; /* Override display: none */
        top: 0.2in !important;
        left: 0.2in !important;
        right: 0.2in !important;
        bottom: 0.2in !important;
        border: 2px solid #B8860B !important;
        border-radius: 44px !important;
        opacity: 1 !important;
    }

    .receipt-table,
    .receipt-table tr {
        page-break-inside: avoid;
    }

    .payment-option-button {
        border: 1px solid #333 !important;
        background: white !important;
    }

    .signature-line-space {
        border-bottom: 1px solid #333 !important;
    }
}