/* KMS PC Receipt Maker - 主樣式文件 */

/* 全局樣式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background: #0095ff;
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.05"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    z-index: -1;
}

/* Modern Container and Layout */
.kms-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    min-height: 100vh;
}

.kms-main-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 0;
}

.kms-content-wrapper {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

/* Modern KMS Navigation */
.kms-navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.kms-nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.kms-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
    text-decoration: none;
}

.kms-brand-icon {
    font-size: 2rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.kms-brand-text {
    background: linear-gradient(45deg, #ffffff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.kms-nav-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.kms-nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    color: white;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.kms-nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.kms-nav-btn:hover::before {
    left: 100%;
}

.kms-nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.kms-nav-btn.active {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    border-color: #ffd700;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.kms-nav-btn.active:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
}

.kms-language-group {
    display: flex;
    gap: 0.5rem;
    margin-left: 1rem;
    padding-left: 1rem;
    border-left: 2px solid rgba(255, 255, 255, 0.2);
}

.kms-lang-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.kms-lang-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.kms-lang-btn.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.kms-mobile-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.kms-mobile-toggle span {
    width: 25px;
    height: 3px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.navbar {
    backdrop-filter: blur(20px);
    background: rgba(13, 110, 253, 0.9) !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

/* 卡片樣式 */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: #ff6e00;
    border-bottom: none;
    border-radius: 15px 15px 0 0 !important;
    padding: 4px 10px;
}

.card-header h5, .card-header h6 {
    margin: 0;
    font-weight: 600;
}

.card-title {
    font-weight: 600;
    color: #333;
}

.card-body {
    padding: 1.5rem;
    background-color: #ffb400;
}

/* 表單樣式 */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

/* 按鈕樣式 */
.btn {
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    padding: 0.75rem 1.5rem;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: #0095ff;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    box-shadow: 0 4px 15px rgba(17, 153, 142, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    box-shadow: 0 4px 15px rgba(255, 65, 108, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
}

.btn-lg {
    font-size: 14px;
    border-radius: 50px;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 0.2rem;
    min-width: 28px;
    height: 24px;
}

.btn-group .btn {
    margin: 0 1px;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-group-vertical .btn-xs {
    margin-bottom: 2px;
}

.btn-group-vertical .btn-xs:last-child {
    margin-bottom: 0;
}

/* 區域切換 */
.section {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.section.active {
    display: block !important;
}

/* 項目列表優化樣式 */
.item-row {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid #e9ecef;
    border-radius: 15px;
    transition: all 0.3s ease;
    padding: 1rem;
    margin-bottom: 1rem;
}

.item-row:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.item-row .btn-group {
    flex-wrap: nowrap;
}

.item-row .btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 8px;
    margin: 0 2px;
}

/* Legacy Receipt Item */
.legacy-receipt-item {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.legacy-receipt-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.125rem 0.25rem rgba(13, 110, 253, 0.15);
}

.legacy-receipt-item .remove-item {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
}

.legacy-receipt-item .remove-item:hover {
    background: #bb2d3b;
    transform: scale(1.1);
}

/* 美化增強樣式 */
.receipt-item-row {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.receipt-item-row:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

/* 預覽區域 */
.receipt-preview {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 15px;
    padding: 2rem;
    min-height: 400px;
    transition: all 0.3s ease;
}

.receipt-preview.has-content {
    border-style: solid;
    border-color: #667eea;
    background: white;
}

/* 歷史記錄樣式 */
.receipt-history-item {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.receipt-history-item:hover {
    background-color: #f8f9fa;
    border-color: #0d6efd !important;
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.receipt-history-item .btn {
    transition: all 0.2s ease-in-out;
}

.receipt-history-item .btn:hover {
    transform: scale(1.05);
}

.receipt-number {
    font-weight: 600;
    color: var(--primary-color);
}

.receipt-amount {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--success-color);
}

/* 預設項目樣式 */
.preset-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    border-radius: 15px;
    transition: all 0.3s ease;
    height: 60px;
    display: flex;
    align-items: center;
}

.preset-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

#presetList {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
    border-radius: 10px;
    padding: 1rem;
}

.preset-item .btn-group {
    flex-wrap: wrap;
    gap: 0.25rem;
}

.preset-item .btn-group .btn {
    margin: 0.125rem;
}

/* 拖拽排序樣式 */
.order-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.order-input {
    width: 50px;
    text-align: center;
    font-size: 0.8rem;
}

.drag-handle {
    cursor: grab;
    color: #6c757d;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.drag-handle:hover {
    color: #667eea;
    background-color: rgba(102, 126, 234, 0.1);
}

.drag-handle:active {
    cursor: grabbing;
}

.preset-item[draggable="true"] {
    cursor: move;
}

.preset-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.preset-item.drag-over {
    border-top: 3px solid #667eea !important;
    transform: translateY(-2px);
}

/* 配置項目樣式 */
.configuration-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.configuration-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

/* Logo 預覽樣式 */
.logo-preview {
    animation: slideInUp 0.3s ease-out;
}

.logo-preview .card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.logo-preview .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

#logoPreviewImage {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

#logoPreviewImage:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

/* 模態框樣式 */
.modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.98);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 20px 20px 0 0;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0 0 20px 20px;
    padding: 1.5rem;
}

#presetModal .modal-dialog {
    min-width: 75%;
    min-height: 75%;
}

#presetModal .modal-content {
    min-height: 75vh;
    background: linear-gradient(135deg, #20b2aa 0%, #008b8b 100%);
}

#receiptDetailsModal .modal-dialog {
    max-width: 90%;
}

#receiptDetailsModal .card {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

#receiptDetailsModal .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

#receiptDetailsModal .table {
    margin-bottom: 0;
}

#receiptDetailsModal .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

#receiptDetailsModal .table td {
    vertical-align: middle;
    font-size: 0.875rem;
}

#receiptDetailsModal .badge {
    font-size: 0.75rem;
}

#receiptDetailsModal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* 總計顯示區域優化 */
#totalsDisplay .card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

#totalsDisplay .card-body {
    padding: 1.5rem;
}

#totalsDisplay .row {
    margin-bottom: 0.5rem;
    align-items: center;
}

#totalsDisplay hr {
    margin: 1rem 0;
    border-top: 2px solid #667eea;
}

/* 徽章 */
.badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
}

/* 訊息提示 */
.alert {
    border-radius: 15px;
    border: none;
    font-weight: 500;
    backdrop-filter: blur(10px);
    animation: slideInDown 0.3s ease-out;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d1edff 0%, #a7d8f0 100%);
    color: #0c5460;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* 工具提示 */
.tooltip {
    font-size: 0.875rem;
}

/* 自定義滾動條 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 載入狀態 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Batch Delete Controls */
.batch-controls {
    border: 1px solid #dee2e6;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    animation: slideDown 0.3s ease-out;
}

.receipt-checkbox {
    transform: scale(1.2);
    margin-right: 0.5rem;
}

.receipt-checkbox:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.receipt-history-item:has(.receipt-checkbox:checked) {
    background-color: #e7f3ff;
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.1);
}

.btn-outline-danger:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.batch-controls .btn {
    transition: all 0.2s ease-in-out;
}

.batch-controls .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-outline-info:hover,
.btn-outline-warning:hover {
    transform: scale(1.05);
}

/* 動畫 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInDown {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.container {
    animation: slideInUp 0.6s ease-out;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .kms-nav-container {
        padding: 1rem;
    }

    .kms-nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        flex-direction: column;
        padding: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .kms-nav-menu.active {
        display: flex;
    }

    .kms-mobile-toggle {
        display: flex;
    }

    .kms-language-group {
        margin-left: 0;
        padding-left: 0;
        border-left: none;
        border-top: 2px solid rgba(255, 255, 255, 0.2);
        padding-top: 1rem;
        margin-top: 1rem;
    }

    .kms-brand-text {
        font-size: 1.2rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        margin-bottom: 0.5rem;
    }

    .d-flex.gap-2.flex-wrap .btn {
        flex: 1 1 auto;
        min-width: 120px;
    }

    .legacy-receipt-item {
        padding: 0.75rem;
    }

    .navbar-brand {
        font-size: 1rem;
    }

    .form-group.mb-0 {
        margin-bottom: 0.5rem !important;
    }

    .d-flex.gap-2.align-items-center.flex-wrap {
        flex-direction: column;
        align-items: stretch !important;
        gap: 0.5rem !important;
    }

    .d-flex.gap-2.align-items-center.flex-wrap > * {
        margin-bottom: 0.5rem;
    }
    
    .batch-controls {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .batch-controls .d-flex {
        flex-direction: column;
        align-items: stretch !important;
        gap: 0.5rem;
    }
    
    .receipt-history-item .col-md-1,
    .receipt-history-item .col-md-2 {
        flex: 0 0 auto;
        width: auto;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .receipt-preview {
        min-height: 300px;
        padding: 1rem;
    }
}

/* 列印樣式 */
@media print {
    #receiptDetailsModal .modal-header,
    #receiptDetailsModal .modal-footer {
        display: none !important;
    }
    
    #receiptDetailsModal .modal-body {
        max-height: none !important;
        overflow: visible !important;
    }
    
    #receiptDetailsModal .card {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
}