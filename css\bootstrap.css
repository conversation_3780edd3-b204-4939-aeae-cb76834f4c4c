/* Bootstrap Replacement CSS - KMS PC Receipt Maker */
/* 替代 Bootstrap 的標準 CSS 類 */

/* 1. CSS 變數定義 (Custom Properties) */
:root {
    /* 主題顏色 */
    --primary-color: #0d6efd;
    --secondary-color: #ffe600;
    --success-color: #0ce47f;
    --info-color: #0dcaf0;
    --warning-color: #ff0000;
    --danger-color: #ff0000;
    --light-color: #7be8b5;
    --dark-color: #212529;
    --white-color: #fff;

    /* 邊框與背景顏色 */
    --border-color: #ffffffb0;
    --body-bg: #ffbb00;
    --body-color: #212529;

    /* 字體 */
    --font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
    --font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --font-size-base: 16px;
    --font-weight-bold: 700;

    /* 間距 (Spacing) */
    --spacer-1: 2px;
    --spacer-2: 3px;
    --spacer-3: 8px;
    --spacer-4: 10px;

    /* 圓角 */
    --border-radius: 12px;
    --border-radius-sm: 10px;
    --border-radius-lg: 16px;
}

/* 2. 通用與佈局 (Container & Layout) */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

/* 3. 網格系統 (Grid System) - 透過通用選擇器簡化 */
[class^="col-"] {
    padding: 0 15px;
    box-sizing: border-box;
}

.col-12 { flex: 0 0 100%; max-width: 100%; }
.col-md-1 { flex: 0 0 10%; max-width: 10%; }
.col-md-2 { flex: 0 0 12%; max-width: 12%; }
.col-md-3 { flex: 0 0 25%; max-width: 25%; }
.col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-md-5 { flex: 0 0 40%; max-width: 40%; }
.col-md-6 { flex: 0 0 50%; max-width: 50%; }
.col-lg-12 { flex: 0 0 100%; max-width: 100%; }

@media (max-width: 768px) {
    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* 4. Flexbox 工具 */
.d-flex { display: flex; }
.d-inline-block { display: inline-block; }
.justify-content-between { justify-content: space-between; }
.justify-content-center { justify-content: center; }
.align-items-center { align-items: center; }
.flex-wrap { flex-wrap: wrap; }
.gap-2 { gap: var(--spacer-2); }
.gap-3 { gap: var(--spacer-3); }

/* 5. 間距 (Spacing) - 使用變數 */
.m-0 { margin: 0; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacer-1); }
.mb-2 { margin-bottom: var(--spacer-2); }
.mb-3 { margin-bottom: var(--spacer-3); }
.me-1 { margin-right: var(--spacer-1); }
.me-2 { margin-right: var(--spacer-2); }
.ms-auto { margin-left: auto; }
.mt-3 { margin-top: var(--spacer-3); }
.mt-4 { margin-top: var(--spacer-4); }
.my-4 { margin-top: var(--spacer-4); margin-bottom: var(--spacer-4); }
.p-3 { padding: var(--spacer-3); }
.pb-2 { padding-bottom: var(--spacer-2); }
.py-4 { padding-top: var(--spacer-4); padding-bottom: var(--spacer-4); }

/* 6. 文字 (Text) */
.text-center { text-align: center; }
.text-end { text-align: right; }
.text-muted { color: var(--secondary-color); }
.text-primary { color: var(--primary-color); }
.fw-bold { font-weight: var(--font-weight-bold); }

/* 7. 背景 (Background) */
.bg-primary { background-color: var(--primary-color); }

/* 8. 按鈕 (Buttons) - 使用變數重構 */
.btn {
    display: inline-block;
    padding: 3px 8px;
    font-size: var(--font-size-base);
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* 實心按鈕 */
.btn-primary { color: var(--white-color); background-color: var(--primary-color); border-color: var(--primary-color); }
.btn-primary:hover { background-color: #0b5ed7; border-color: #0a58ca; }

.btn-secondary { color: var(--white-color); background-color: var(--secondary-color); border-color: var(--secondary-color); }
.btn-secondary:hover { background-color: #5c636a; border-color: #565e64; }

.btn-success { color: var(--white-color); background-color: var(--success-color); border-color: var(--success-color); }
.btn-success:hover { background-color: #157347; border-color: #146c43; }

.btn-info { color: var(--dark-color); background-color: var(--info-color); border-color: var(--info-color); }
.btn-info:hover { background-color: #31d2f2; border-color: #25cff2; }

.btn-danger { color: var(--white-color); background-color: var(--danger-color); border-color: var(--danger-color); }
.btn-danger:hover { background-color: #c82333; border-color: #bd2130; }

/* 外框線按鈕 */
.btn-outline-primary { color: var(--primary-color); border-color: var(--primary-color); background-color: transparent; }
.btn-outline-primary:hover { color: var(--white-color); background-color: var(--primary-color); }

.btn-outline-secondary { color: var(--secondary-color); border-color: var(--secondary-color); background-color: transparent; }
.btn-outline-secondary:hover { color: var(--white-color); background-color: var(--secondary-color); }

.btn-outline-success { color: var(--success-color); border-color: var(--success-color); background-color: transparent; }
.btn-outline-success:hover { color: var(--white-color); background-color: var(--success-color); }

.btn-outline-info { color: var(--info-color); border-color: var(--info-color); background-color: transparent; }
.btn-outline-info:hover { color: var(--dark-color); background-color: var(--info-color); }

.btn-outline-warning { color: var(--warning-color); border-color: var(--warning-color); background-color: transparent; }
.btn-outline-warning:hover { color: var(--dark-color); background-color: var(--warning-color); }

.btn-outline-danger { color: var(--danger-color); border-color: var(--danger-color); background-color: transparent; }
.btn-outline-danger:hover { color: var(--white-color); background-color: var(--danger-color); }

/* 按鈕尺寸 */
.btn-sm { padding: 4px 8px; font-size: 12px; border-radius: var(--border-radius-sm); }
.btn-lg { padding: 8px 16px; font-size: 16px; border-radius: var(--border-radius-lg); }

/* 按鈕群組 (Button Group) - 合併重複選擇器 */
.btn-group {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
}
.btn-group .btn {
    position: relative;
    flex: 1 1 auto;
}
.btn-group .btn:not(:first-child) {
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.btn-group .btn:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* ... 其他樣式 ... */

/* 9. 卡片 (Cards) - 修正命名 */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--white-color);
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: var(--border-radius);
}

.card-header {
    padding: 0.5rem 1rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-top-left-radius: calc(var(--border-radius) - 1px);
    border-top-right-radius: calc(var(--border-radius) - 1px);
}

.card-body {
    flex: 1 1 auto;
    padding: 12px;
}

.card-title {
    margin-bottom: 6px;
    font-size: 16px;
    font-weight: 500;
}

/* 10. 彈出視窗 (Modal) */
.modal-lg {
    max-width: 800px;
}

@media (min-width: 576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
